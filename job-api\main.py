# main.py
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status, Response
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from jobs import router as jobs_router        # ← imports jobs router
from schema import router as schema_router    # ← imports schema router
from personalized_search import router as personalized_search_router  # ← imports personalized search router
from pydantic import BaseModel, validator
from supabase_client import supabase
from typing import Optional, List, Dict, Any
import traceback
import asyncio
import httpx
from datetime import datetime
from jooble_service import jooble, map_jooble_job
from muse_service import muse, map_muse_job
from job_service import adzuna, arbeitnow

app = FastAPI(title="Jobbify API")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ─── Models ────────────────────────────────────────────────────────────

class SwipeIn(BaseModel):
    job_id: str
    direction: str  # 'left' or 'right'
    profile_id: str  # Adding profile_id to support current app structure
    
    # Validate that direction is either 'left' or 'right'
    @validator('direction')
    def validate_direction(cls, v):
        if v not in ['left', 'right']:
            raise ValueError('direction must be either "left" or "right"')
        return v

class BookmarkIn(BaseModel):
    job_id: str
    profile_id: str  # Adding profile_id to support current app structure

class ApplicationIn(BaseModel):
    job_id: str
    profile_id: str  # Adding profile_id to support current app structure
    cover_letter: Optional[str] = None

class JobSearchRequest(BaseModel):
    keywords: str
    location: str
    job_type: Optional[str] = None
    experience_level: Optional[str] = None
    min_salary: Optional[int] = None
    max_salary: Optional[int] = None

# ─── Dependency to get current user ID ───────────────────────────────────
# For now, we'll use a simpler approach without auth verification
def get_user_id(profile_id: str = None) -> str:
    if not profile_id:
        raise HTTPException(status_code=401, detail="Missing profile_id")
    return profile_id

# ─── Endpoints ────────────────────────────────────────────────────────

@app.get("/jobs/")
def fetch_jobs(limit: int = 50) -> List[Dict[str, Any]]:
    """Fetch available jobs."""
    try:
        resp = supabase.table("jobs").select("*").limit(limit).execute()
        return resp.data
    except Exception as e:
        # Return mock data if Supabase fails
        print(f"Supabase error: {str(e)}")
        return [
            {
                "id": "test-job-1",
                "title": "Senior Software Engineer",
                "company": "Tech Corp",
                "location": "Remote",
                "description": "We are looking for a senior software engineer to join our team.",
                "salary": "$120,000 - $150,000",
                "url": "https://example.com/job1",
                "created_at": "2024-01-01T00:00:00Z"
            },
            {
                "id": "test-job-2",
                "title": "Frontend Developer",
                "company": "Design Studio",
                "location": "New York, NY",
                "description": "Join our creative team as a frontend developer.",
                "salary": "$90,000 - $110,000",
                "url": "https://example.com/job2",
                "created_at": "2024-01-02T00:00:00Z"
            }
        ][:limit]

@app.get("/jobs/unseen")
def fetch_unseen_jobs(limit: int = 20, profile_id: str = None) -> List[Dict[str, Any]]:
    """Call the RPC unseen_jobs to return jobs obeying your rules."""
    user_id = get_user_id(profile_id)
    try:
        resp = supabase.rpc("unseen_jobs", {"_limit": limit, "user_id": user_id}).execute()
        return resp.data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/jobs/search")
async def search_jobs_realtime(request: JobSearchRequest) -> Dict[str, Any]:
    """Search for jobs in real-time based on user preferences"""
    try:
        print(f"🔍 Real-time job search: {request.keywords} in {request.location}")

        # Fetch jobs from multiple APIs concurrently
        async with httpx.AsyncClient() as client:
            tasks = []

            # Jooble API
            tasks.append(jooble(client, request.keywords, request.location))

            # The Muse API (if relevant category)
            if any(keyword in request.keywords.lower() for keyword in ['technology', 'computer', 'software', 'developer', 'engineer']):
                tasks.append(muse(client, "Computer and IT", request.location))

            # Adzuna API
            tasks.append(adzuna(client))

            # Arbeitnow API
            tasks.append(arbeitnow(client))

            # Execute all API calls concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            all_jobs = []

            # Process Jooble results
            if len(results) > 0 and not isinstance(results[0], Exception):
                jooble_jobs = results[0]
                for job in jooble_jobs:
                    mapped_job = map_jooble_job(job)
                    all_jobs.append(mapped_job)

            # Process Muse results
            if len(results) > 1 and not isinstance(results[1], Exception):
                muse_jobs = results[1]
                for job in muse_jobs:
                    mapped_job = map_muse_job(job)
                    all_jobs.append(mapped_job)

            # Process other API results
            for i, result in enumerate(results[2:], start=2):
                if not isinstance(result, Exception):
                    for job in result:
                        # Add source information
                        job['source'] = 'adzuna' if i == 2 else 'arbeitnow'
                        all_jobs.append(job)

        # Filter jobs based on search criteria
        filtered_jobs = filter_jobs_by_criteria(all_jobs, request)

        # Remove duplicates
        unique_jobs = remove_duplicate_jobs(filtered_jobs)

        # Sort by relevance
        sorted_jobs = sort_jobs_by_relevance(unique_jobs, request)

        print(f"✅ Found {len(sorted_jobs)} relevant jobs")

        return {
            "jobs": sorted_jobs[:50],  # Limit to 50 jobs
            "total_found": len(sorted_jobs),
            "search_params": request.dict()
        }

    except Exception as e:
        print(f"❌ Error in real-time job search: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Job search failed: {str(e)}")

def filter_jobs_by_criteria(jobs: List[Dict], request: JobSearchRequest) -> List[Dict]:
    """Filter jobs based on search criteria"""
    filtered = []

    for job in jobs:
        # Location filter
        if request.location.lower() != 'remote':
            job_location = job.get('location', '').lower()
            if request.location.lower() not in job_location and 'remote' not in job_location:
                continue

        # Salary filter
        if request.min_salary or request.max_salary:
            job_salary = job.get('salary', '')
            if job_salary:
                # Extract salary numbers (basic implementation)
                import re
                salary_numbers = re.findall(r'\d+', str(job_salary))
                if salary_numbers:
                    job_salary_num = int(salary_numbers[0])
                    if request.min_salary and job_salary_num < request.min_salary:
                        continue
                    if request.max_salary and job_salary_num > request.max_salary:
                        continue

        # Job type filter
        if request.job_type:
            job_title = job.get('title', '').lower()
            job_description = job.get('description', '').lower()
            if request.job_type.lower() not in job_title and request.job_type.lower() not in job_description:
                continue

        filtered.append(job)

    return filtered

def remove_duplicate_jobs(jobs: List[Dict]) -> List[Dict]:
    """Remove duplicate jobs based on title and company"""
    seen = set()
    unique_jobs = []

    for job in jobs:
        title = job.get('title', '').lower().strip()
        company = job.get('company', '').lower().strip()
        key = f"{title}_{company}"

        if key not in seen:
            seen.add(key)
            unique_jobs.append(job)

    return unique_jobs

def sort_jobs_by_relevance(jobs: List[Dict], request: JobSearchRequest) -> List[Dict]:
    """Sort jobs by relevance to search criteria"""
    def calculate_relevance_score(job):
        score = 0
        title = job.get('title', '').lower()
        description = job.get('description', '').lower()
        location = job.get('location', '').lower()

        # Keyword relevance
        keywords = request.keywords.lower().split()
        for keyword in keywords:
            if keyword in title:
                score += 10
            if keyword in description:
                score += 5

        # Location relevance
        if request.location.lower() in location:
            score += 8
        elif 'remote' in location and request.location.lower() == 'remote':
            score += 10

        # Recent posting bonus (if available)
        if 'posted_date' in job:
            # Add bonus for recent postings
            score += 2

        return score

    # Sort by relevance score (descending)
    return sorted(jobs, key=calculate_relevance_score, reverse=True)

@app.get("/jobs/for-user/{user_id}")
async def get_jobs_for_user(user_id: str, limit: int = 20) -> Dict[str, Any]:
    """Get personalized jobs for a user based on their preferences"""
    try:
        # Get user preferences from database
        resp = supabase.table("user_job_preferences").select("*").eq("user_id", user_id).execute()

        if not resp.data:
            raise HTTPException(status_code=404, detail="User preferences not found")

        preferences = resp.data[0]
        print(f"🎯 Fetching personalized jobs for user: {user_id}")

        # Generate search requests based on user preferences
        search_requests = []

        # Create searches for each location and job type combination
        for location in preferences.get('preferred_locations', ['Remote'])[:3]:  # Limit to 3 locations
            for job_type in preferences.get('preferred_job_types', ['Full-time'])[:2]:  # Limit to 2 job types
                # Use industries as keywords if available
                if preferences.get('preferred_industries'):
                    for industry in preferences.get('preferred_industries', [])[:2]:  # Limit to 2 industries
                        search_requests.append(JobSearchRequest(
                            keywords=f"{industry} {job_type}",
                            location=location,
                            job_type=job_type,
                            experience_level=preferences.get('experience_level'),
                            min_salary=preferences.get('min_salary'),
                            max_salary=preferences.get('max_salary')
                        ))
                else:
                    search_requests.append(JobSearchRequest(
                        keywords=job_type,
                        location=location,
                        job_type=job_type,
                        experience_level=preferences.get('experience_level'),
                        min_salary=preferences.get('min_salary'),
                        max_salary=preferences.get('max_salary')
                    ))

        # Limit total searches to prevent API overuse
        search_requests = search_requests[:6]

        all_jobs = []

        # Execute searches
        for search_request in search_requests:
            try:
                result = await search_jobs_realtime(search_request)
                all_jobs.extend(result.get('jobs', []))

                # Add delay between API calls to respect rate limits
                await asyncio.sleep(0.5)
            except Exception as search_error:
                print(f"⚠️ Search failed for {search_request.keywords}: {search_error}")
                continue

        # Remove duplicates and limit results
        unique_jobs = remove_duplicate_jobs(all_jobs)
        final_jobs = unique_jobs[:limit]

        print(f"✅ Found {len(final_jobs)} personalized jobs for user")

        return {
            "jobs": final_jobs,
            "total_found": len(unique_jobs),
            "user_preferences": preferences,
            "searches_performed": len(search_requests)
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error fetching jobs for user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch personalized jobs: {str(e)}")

@app.post("/swipe", status_code=status.HTTP_201_CREATED)
def swipe(sw: SwipeIn) -> Dict[str, Any]:
    """Insert a swipe record."""
    try:
        user_id = get_user_id(sw.profile_id)
        payload = {
            "user_id": user_id,
            "job_id": sw.job_id,
            "direction": sw.direction,
        }
        
        # Check if there's an existing swipe first
        print(f"Checking if swipe already exists: user_id={user_id}, job_id={sw.job_id}")
        try:
            existing = supabase.table("swipes").select("id").eq("user_id", user_id).eq("job_id", sw.job_id).execute()
            
            if existing.data and len(existing.data) > 0:
                # Swipe already exists, update it
                swipe_id = existing.data[0]["id"]
                print(f"Swipe exists with ID {swipe_id}, updating direction to {sw.direction}")
                
                try:
                    resp = supabase.table("swipes").update({"direction": sw.direction}).eq("id", swipe_id).execute()
                    print(f"Update response: {resp.data}")
                    return resp.data[0] if resp.data and len(resp.data) > 0 else {"id": swipe_id, "direction": sw.direction}
                except Exception as update_error:
                    print(f"Error updating swipe: {str(update_error)}")
                    print(traceback.format_exc())
                    # Return existing data as success instead of failing
                    return {"id": swipe_id, "message": "Record exists but could not be updated"}
            else:
                # No existing swipe, insert new one
                print(f"No existing swipe found, inserting new record with payload: {payload}")
                try:
                    resp = supabase.table("swipes").insert(payload).execute()
                    print(f"Insert response: {resp.data}")
                    return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
                except Exception as insert_error:
                    print(f"Error inserting swipe: {str(insert_error)}")
                    print(traceback.format_exc())
                    raise HTTPException(status_code=400, detail=f"Failed to insert swipe: {str(insert_error)}")
        except Exception as query_error:
            print(f"Error querying existing swipes: {str(query_error)}")
            print(traceback.format_exc())
            # Try direct insert as fallback
            try:
                resp = supabase.table("swipes").insert(payload).execute()
                return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
            except Exception as fallback_error:
                print(f"Fallback insert failed: {str(fallback_error)}")
                print(traceback.format_exc())
                raise HTTPException(status_code=400, detail=f"Failed to insert swipe: {str(fallback_error)}")
    except Exception as e:
        print(f"Unhandled error in swipe endpoint: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/bookmarks", status_code=status.HTTP_201_CREATED)
def bookmark(bm: BookmarkIn) -> Dict[str, Any]:
    """Insert a bookmark."""
    try:
        user_id = get_user_id(bm.profile_id)
        
        # Check if bookmark already exists
        print(f"Checking if bookmark already exists: profile_id={user_id}, job_id={bm.job_id}")
        try:
            existing = supabase.table("bookmarks").select("id").eq("profile_id", user_id).eq("job_id", bm.job_id).execute()
            
            if existing.data and len(existing.data) > 0:
                # Bookmark already exists, return success
                bookmark_id = existing.data[0]["id"]
                print(f"Bookmark already exists with ID {bookmark_id}")
                return existing.data[0]
            
            # No existing bookmark, insert new one
            print(f"No existing bookmark found, inserting new record")
            try:
                resp = supabase.table("bookmarks").insert({
                    "profile_id": user_id,
                    "job_id": bm.job_id
                }).execute()
                
                print(f"Insert response: {resp.data}")
                return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
            except Exception as insert_error:
                print(f"Error inserting bookmark: {str(insert_error)}")
                print(traceback.format_exc())
                raise HTTPException(status_code=400, detail=f"Failed to insert bookmark: {str(insert_error)}")
        except Exception as query_error:
            print(f"Error querying existing bookmarks: {str(query_error)}")
            print(traceback.format_exc())
            # Try direct insert as fallback
            try:
                resp = supabase.table("bookmarks").insert({
                    "profile_id": user_id,
                    "job_id": bm.job_id
                }).execute()
                return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
            except Exception as fallback_error:
                print(f"Fallback insert failed: {str(fallback_error)}")
                print(traceback.format_exc())
                raise HTTPException(status_code=400, detail=f"Failed to insert bookmark: {str(fallback_error)}")
    except Exception as e:
        print(f"Unhandled error in bookmark endpoint: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/applications", status_code=status.HTTP_201_CREATED)
def apply(apply_in: ApplicationIn) -> Dict[str, Any]:
    """Insert an application record."""
    try:
        user_id = get_user_id(apply_in.profile_id)
        payload = {
            "profile_id": user_id,
            "job_id": apply_in.job_id,
            "cover_letter": apply_in.cover_letter,
            "status": "applying"
        }
        
        # Check if application already exists
        print(f"Checking if application already exists: profile_id={user_id}, job_id={apply_in.job_id}")
        try:
            existing = supabase.table("applications").select("id").eq("profile_id", user_id).eq("job_id", apply_in.job_id).execute()
            
            if existing.data and len(existing.data) > 0:
                # Application already exists, return success
                app_id = existing.data[0]["id"]
                print(f"Application already exists with ID {app_id}")
                return existing.data[0]
            
            # No existing application, insert new one
            print(f"No existing application found, inserting new record")
            try:
                resp = supabase.table("applications").insert(payload).execute()
                
                print(f"Insert response: {resp.data}")
                return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
            except Exception as insert_error:
                print(f"Error inserting application: {str(insert_error)}")
                print(traceback.format_exc())
                raise HTTPException(status_code=400, detail=f"Failed to insert application: {str(insert_error)}")
        except Exception as query_error:
            print(f"Error querying existing applications: {str(query_error)}")
            print(traceback.format_exc())
            # Try direct insert as fallback
            try:
                resp = supabase.table("applications").insert(payload).execute()
                return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
            except Exception as fallback_error:
                print(f"Fallback insert failed: {str(fallback_error)}")
                print(traceback.format_exc())
                raise HTTPException(status_code=400, detail=f"Failed to insert application: {str(fallback_error)}")
    except Exception as e:
        print(f"Unhandled error in apply endpoint: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/matches", status_code=status.HTTP_201_CREATED)
def save_match(apply_in: ApplicationIn) -> Dict[str, Any]:
    """Insert a match record for backwards compatibility."""
    try:
        user_id = get_user_id(apply_in.profile_id)
        payload = {
            "profile_id": user_id,
            "job_id": apply_in.job_id,
            "status": "applying",
            "created_at": "now()"
        }
        
        # Check if a match already exists
        print(f"Checking if match already exists: profile_id={user_id}, job_id={apply_in.job_id}")
        try:
            existing = supabase.table("matches").select("id").eq("profile_id", user_id).eq("job_id", apply_in.job_id).execute()
            
            if existing.data and len(existing.data) > 0:
                # Match already exists, return success
                match_id = existing.data[0]["id"]
                print(f"Match already exists with ID {match_id}")
                return existing.data[0]
            
            # No existing match, insert new one
            print(f"No existing match found, inserting new record")
            try:
                resp = supabase.table("matches").insert(payload).execute()
                
                print(f"Insert response: {resp.data}")
                return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
            except Exception as insert_error:
                print(f"Error inserting match: {str(insert_error)}")
                print(traceback.format_exc())
                raise HTTPException(status_code=400, detail=f"Failed to insert match: {str(insert_error)}")
        except Exception as query_error:
            print(f"Error querying existing matches: {str(query_error)}")
            print(traceback.format_exc())
            # Try direct insert as fallback
            try:
                resp = supabase.table("matches").insert(payload).execute()
                return resp.data[0] if resp.data and len(resp.data) > 0 else {"message": "Inserted but no data returned"}
            except Exception as fallback_error:
                print(f"Fallback insert failed: {str(fallback_error)}")
                print(traceback.format_exc())
                raise HTTPException(status_code=400, detail=f"Failed to insert match: {str(fallback_error)}")
    except Exception as e:
        print(f"Unhandled error in save_match endpoint: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(status_code=400, detail=str(e))

# Add a health check endpoint
@app.get("/health")
def health_check():
    return {"status": "ok"}

app.include_router(jobs_router)
app.include_router(schema_router)
app.include_router(personalized_search_router)  # New personalized search system

# Add cache and monitoring endpoints
@app.on_event("startup")
async def startup_event():
    """Initialize cache on startup"""
    from cache import init_cache
    await init_cache()

@app.on_event("shutdown")
async def shutdown_event():
    """Close cache on shutdown"""
    from cache import close_cache
    await close_cache()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    from cache import cache
    from api_usage import get_api_health_status

    cache_status = "connected" if cache.connected else "disconnected"
    api_health = await get_api_health_status()

    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "cache": {"status": cache_status},
        "apis": api_health
    }

@app.get("/metrics")
async def get_metrics():
    """Prometheus-style metrics endpoint"""
    from cache import cache
    from api_usage import usage_tracker

    cache_stats = await cache.get_cache_stats()
    usage_stats = await usage_tracker.get_all_usage_stats()

    # Format as Prometheus metrics
    metrics = []

    # Cache metrics
    if cache_stats.get("status") == "connected":
        metrics.extend([
            f"cache_hits_total {cache_stats.get('cache_hits', 0)}",
            f"cache_misses_total {cache_stats.get('cache_misses', 0)}",
            f"cache_sets_total {cache_stats.get('cache_sets', 0)}",
            f"cache_errors_total {cache_stats.get('cache_errors', 0)}",
            f"cache_hit_ratio {cache_stats.get('hit_ratio', 0)}",
            f"cache_keys_total {cache_stats.get('redis_keys', 0)}"
        ])

    # API usage metrics
    for api_name, stats in usage_stats.get("apis", {}).items():
        metrics.extend([
            f'api_calls_total{{api_name="{api_name}"}} {stats["calls_made"]}',
            f'api_quota_remaining{{api_name="{api_name}"}} {stats["quota_remaining"]}',
            f'api_usage_percentage{{api_name="{api_name}"}} {stats["usage_percentage"]}',
            f'api_estimated_cost{{api_name="{api_name}"}} {stats["estimated_cost"]}'
        ])

    return Response(content="\n".join(metrics), media_type="text/plain")

@app.get("/cache/stats")
async def get_cache_stats():
    """Get detailed cache statistics"""
    from cache import cache
    return await cache.get_cache_stats()

@app.get("/api-usage/stats")
async def get_api_usage_stats():
    """Get API usage statistics"""
    from api_usage import usage_tracker
    return await usage_tracker.get_all_usage_stats()

# Add this at the end of file to bind to all interfaces when run directly
if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
