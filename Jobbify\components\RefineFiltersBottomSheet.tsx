import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  Switch,
  Animated,
  PanResponder,
  Dimensions,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

interface JobFilters {
  jobTypes: string[];
  minSalary: number;
  maxSalary: number;
  remoteOnly: boolean;
  experienceLevel: string[];
}

interface RefineFiltersBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  filters: JobFilters;
  onApplyFilters: (filters: JobFilters) => void;
}

const JOB_TYPES = ['Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship'];
const EXPERIENCE_LEVELS = ['Entry', 'Junior', 'Mid', 'Senior', 'Lead', 'Executive'];

export const RefineFiltersBottomSheet: React.FC<RefineFiltersBottomSheetProps> = ({
  visible,
  onClose,
  filters,
  onApplyFilters
}) => {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const [localFilters, setLocalFilters] = useState<JobFilters>(filters);
  const [isDragging, setIsDragging] = useState(false);
  
  const translateY = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const backdropOpacity = useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: SCREEN_HEIGHT,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) => {
        return Math.abs(gestureState.dy) > 10 && gestureState.dy > 0;
      },
      onPanResponderGrant: () => {
        setIsDragging(true);
      },
      onPanResponderMove: (_, gestureState) => {
        if (gestureState.dy > 0) {
          translateY.setValue(gestureState.dy);
          const progress = gestureState.dy / (SCREEN_HEIGHT * 0.3);
          backdropOpacity.setValue(0.5 * (1 - progress));
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        setIsDragging(false);
        
        if (gestureState.dy > SCREEN_HEIGHT * 0.2) {
          onClose();
        } else {
          Animated.parallel([
            Animated.spring(translateY, {
              toValue: 0,
              useNativeDriver: true,
            }),
            Animated.timing(backdropOpacity, {
              toValue: 0.5,
              duration: 200,
              useNativeDriver: true,
            }),
          ]).start();
        }
      },
    })
  ).current;

  const toggleJobType = (jobType: string) => {
    setLocalFilters(prev => ({
      ...prev,
      jobTypes: prev.jobTypes.includes(jobType)
        ? prev.jobTypes.filter(type => type !== jobType)
        : [...prev.jobTypes, jobType]
    }));
  };

  const toggleExperienceLevel = (level: string) => {
    setLocalFilters(prev => ({
      ...prev,
      experienceLevel: prev.experienceLevel.includes(level)
        ? prev.experienceLevel.filter(exp => exp !== level)
        : [...prev.experienceLevel, level]
    }));
  };

  const handleApply = () => {
    onApplyFilters(localFilters);
    onClose();
  };

  const handleReset = () => {
    const resetFilters: JobFilters = {
      jobTypes: [],
      minSalary: 30000,
      maxSalary: 200000,
      remoteOnly: false,
      experienceLevel: []
    };
    setLocalFilters(resetFilters);
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <Animated.View 
          style={[
            styles.backdrop,
            { opacity: backdropOpacity }
          ]}
        >
          <TouchableOpacity 
            style={StyleSheet.absoluteFill} 
            onPress={onClose}
            activeOpacity={1}
          />
        </Animated.View>

        <Animated.View
          style={[
            styles.bottomSheet,
            {
              backgroundColor: themeColors.background,
              transform: [{ translateY }]
            }
          ]}
          {...panResponder.panHandlers}
        >
          {/* Handle */}
          <View style={styles.handle}>
            <View style={[styles.handleBar, { backgroundColor: themeColors.border }]} />
          </View>

          {/* Header */}
          <View style={styles.header}>
            <Text style={[styles.title, { color: themeColors.text }]}>
              Refine Filters
            </Text>
            <TouchableOpacity onPress={onClose}>
              <MaterialIcons name="close" size={24} color={themeColors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Job Types */}
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                Job Types
              </Text>
              <View style={styles.chipContainer}>
                {JOB_TYPES.map((jobType) => (
                  <TouchableOpacity
                    key={jobType}
                    style={[
                      styles.chip,
                      {
                        backgroundColor: localFilters.jobTypes.includes(jobType)
                          ? themeColors.tint
                          : themeColors.card,
                        borderColor: themeColors.border
                      }
                    ]}
                    onPress={() => toggleJobType(jobType)}
                  >
                    <Text
                      style={[
                        styles.chipText,
                        {
                          color: localFilters.jobTypes.includes(jobType)
                            ? '#FFFFFF'
                            : themeColors.text
                        }
                      ]}
                    >
                      {jobType}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Salary Range */}
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                Salary Range
              </Text>
              <Text style={[styles.salaryLabel, { color: themeColors.textSecondary }]}>
                ${localFilters.minSalary.toLocaleString()} - ${localFilters.maxSalary.toLocaleString()}
              </Text>
              
              <View style={styles.sliderContainer}>
                <Text style={[styles.sliderLabel, { color: themeColors.textSecondary }]}>
                  Min: ${localFilters.minSalary.toLocaleString()}
                </Text>
                <Slider
                  style={styles.slider}
                  minimumValue={20000}
                  maximumValue={300000}
                  step={5000}
                  value={localFilters.minSalary}
                  onValueChange={(value) => setLocalFilters(prev => ({ ...prev, minSalary: value }))}
                  minimumTrackTintColor={themeColors.tint}
                  maximumTrackTintColor={themeColors.border}
                />
              </View>

              <View style={styles.sliderContainer}>
                <Text style={[styles.sliderLabel, { color: themeColors.textSecondary }]}>
                  Max: ${localFilters.maxSalary.toLocaleString()}
                </Text>
                <Slider
                  style={styles.slider}
                  minimumValue={localFilters.minSalary + 10000}
                  maximumValue={500000}
                  step={5000}
                  value={localFilters.maxSalary}
                  onValueChange={(value) => setLocalFilters(prev => ({ ...prev, maxSalary: value }))}
                  minimumTrackTintColor={themeColors.tint}
                  maximumTrackTintColor={themeColors.border}
                />
              </View>
            </View>

            {/* Remote Toggle */}
            <View style={styles.section}>
              <View style={styles.switchRow}>
                <View>
                  <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                    Remote Only
                  </Text>
                  <Text style={[styles.switchDescription, { color: themeColors.textSecondary }]}>
                    Show only remote job opportunities
                  </Text>
                </View>
                <Switch
                  value={localFilters.remoteOnly}
                  onValueChange={(value) => setLocalFilters(prev => ({ ...prev, remoteOnly: value }))}
                  trackColor={{ false: themeColors.border, true: themeColors.tint }}
                />
              </View>
            </View>

            {/* Experience Level */}
            <View style={styles.section}>
              <Text style={[styles.sectionTitle, { color: themeColors.text }]}>
                Experience Level
              </Text>
              <View style={styles.chipContainer}>
                {EXPERIENCE_LEVELS.map((level) => (
                  <TouchableOpacity
                    key={level}
                    style={[
                      styles.chip,
                      {
                        backgroundColor: localFilters.experienceLevel.includes(level)
                          ? themeColors.tint
                          : themeColors.card,
                        borderColor: themeColors.border
                      }
                    ]}
                    onPress={() => toggleExperienceLevel(level)}
                  >
                    <Text
                      style={[
                        styles.chipText,
                        {
                          color: localFilters.experienceLevel.includes(level)
                            ? '#FFFFFF'
                            : themeColors.text
                        }
                      ]}
                    >
                      {level}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>

          {/* Footer */}
          <View style={[styles.footer, { borderTopColor: themeColors.border }]}>
            <TouchableOpacity
              style={[styles.resetButton, { borderColor: themeColors.border }]}
              onPress={handleReset}
            >
              <Text style={[styles.resetButtonText, { color: themeColors.text }]}>
                Reset
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.applyButton, { backgroundColor: themeColors.tint }]}
              onPress={handleApply}
            >
              <Text style={styles.applyButtonText}>
                Apply Filters
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000000',
  },
  bottomSheet: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: SCREEN_HEIGHT * 0.8,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  handle: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  handleBar: {
    width: 40,
    height: 4,
    borderRadius: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  chipText: {
    fontSize: 14,
    fontWeight: '500',
  },
  salaryLabel: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 16,
  },
  sliderContainer: {
    marginBottom: 16,
  },
  sliderLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  switchDescription: {
    fontSize: 14,
    marginTop: 4,
  },
  footer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    gap: 12,
  },
  resetButton: {
    flex: 1,
    height: 48,
    borderRadius: 12,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  resetButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  applyButton: {
    flex: 2,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  applyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
