"""
Personalized Job Search Orchestrator
Replaces static job storage with dynamic API calling based on user preferences
"""

import asyncio
import httpx
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, JSONResponse
from pydantic import BaseModel
from supabase_client import supabase
from jooble_service import jooble, map_jooble_job
from muse_service import muse, map_muse_job
from job_service import adzuna, arbeitnow
import json
import logging
from datetime import datetime, timedelta

# Import cache and API tracking
from cache import get_cached_jobs, cache_jobs, init_cache, get_user_search_count, increment_user_searches
from api_usage import (
    call_with_tracking,
    APILimitReached,
    get_api_health_status,
    call_jooble_with_tracking,
    call_adzuna_with_tracking,
    call_muse_with_tracking,
    call_arbeitnow_with_tracking
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/jobs", tags=["personalized-search"])

class PersonalizedSearchRequest(BaseModel):
    user_id: str
    force_refresh: bool = False
    limit: int = 100

class APIResponse(BaseModel):
    jobs: List[Dict[str, Any]]
    total_found: int
    cache_used: bool
    apis_called: List[str]
    search_combinations: int
    user_preferences: Dict[str, Any]

# Industry to API category mapping
INDUSTRY_API_MAPPING = {
    'Technology': {
        'muse_category': 'Computer and IT',
        'jooble_keywords': ['software', 'developer', 'engineer', 'tech'],
        'adzuna_keywords': ['software', 'developer', 'IT']
    },
    'Healthcare': {
        'muse_category': 'Healthcare',
        'jooble_keywords': ['nurse', 'doctor', 'medical', 'healthcare'],
        'adzuna_keywords': ['healthcare', 'medical', 'nurse']
    },
    'Finance': {
        'muse_category': 'Finance',
        'jooble_keywords': ['finance', 'accounting', 'analyst', 'banking'],
        'adzuna_keywords': ['finance', 'accounting', 'banking']
    },
    'Education': {
        'muse_category': 'Education',
        'jooble_keywords': ['teacher', 'education', 'instructor', 'academic'],
        'adzuna_keywords': ['education', 'teacher', 'academic']
    },
    'Marketing & Advertising': {
        'muse_category': 'Marketing',
        'jooble_keywords': ['marketing', 'advertising', 'digital marketing', 'social media'],
        'adzuna_keywords': ['marketing', 'advertising', 'digital']
    }
}

def is_us_location(location: str) -> bool:
    """Check if location is in the US"""
    us_indicators = [
        ', CA', ', NY', ', TX', ', FL', ', IL', ', PA', ', OH', ', GA', ', NC', ', MI',
        'California', 'New York', 'Texas', 'Florida', 'Illinois', 'Pennsylvania',
        'United States', 'USA', 'US'
    ]
    return any(indicator in location for indicator in us_indicators)

def is_remote_preferred(preferences: Dict) -> bool:
    """Check if user prefers remote work"""
    remote_pref = preferences.get('remote_work_preference', '')
    return remote_pref in ['required', 'preferred'] or 'Remote' in preferences.get('preferred_locations', [])

# Remove old caching functions - now using Redis

def generate_cache_key(user_preferences: Dict) -> str:
    """Generate a cache key based on user preferences"""
    locations = sorted(user_preferences.get('preferred_locations', []))
    job_types = sorted(user_preferences.get('preferred_job_types', []))
    industries = sorted(user_preferences.get('preferred_industries', []))
    experience = user_preferences.get('experience_level', 'mid')
    
    key_parts = [
        '_'.join(locations[:3]),  # Limit to 3 locations
        '_'.join(job_types[:2]),  # Limit to 2 job types
        '_'.join(industries[:2]), # Limit to 2 industries
        experience
    ]
    
    return '_'.join(key_parts).lower().replace(' ', '_').replace(',', '')

async def call_apis_for_search(search_params: Dict, user_preferences: Dict) -> List[Dict]:
    """Call multiple APIs with tracking and fallback logic"""
    jobs = []
    apis_called = []

    location = search_params['location']
    keywords = search_params['keywords']

    async with httpx.AsyncClient() as client:
        # Always try Jooble (good international coverage)
        try:
            logger.info(f"🔄 Calling Jooble API for: {keywords} in {location}")
            jooble_jobs = await call_jooble_with_tracking(jooble(client, keywords, location))

            for job in jooble_jobs:
                mapped_job = map_jooble_job(job)
                mapped_job['api_source'] = 'jooble'
                jobs.append(mapped_job)

            apis_called.append('jooble')
            logger.info(f"✅ Jooble: {len(jooble_jobs)} jobs")

        except APILimitReached:
            logger.warning("🚫 Jooble quota exhausted - using cache/fallback")
        except Exception as e:
            logger.error(f"❌ Jooble API failed: {e}")

        # Try Arbeitnow (good for remote and international)
        try:
            logger.info("🔄 Calling Arbeitnow API")
            arbeitnow_jobs = await call_arbeitnow_with_tracking(arbeitnow(client))

            for job in arbeitnow_jobs:
                job['api_source'] = 'arbeitnow'
                jobs.append(job)

            apis_called.append('arbeitnow')
            logger.info(f"✅ Arbeitnow: {len(arbeitnow_jobs)} jobs")

        except APILimitReached:
            logger.warning("🚫 Arbeitnow quota exhausted - using cache/fallback")
        except Exception as e:
            logger.error(f"❌ Arbeitnow API failed: {e}")

        # US-specific APIs
        if is_us_location(location):
            # Try Adzuna
            try:
                logger.info("🔄 Calling Adzuna API")
                adzuna_jobs = await call_adzuna_with_tracking(adzuna(client))

                for job in adzuna_jobs:
                    job['api_source'] = 'adzuna'
                    jobs.append(job)

                apis_called.append('adzuna')
                logger.info(f"✅ Adzuna: {len(adzuna_jobs)} jobs")

            except APILimitReached:
                logger.warning("🚫 Adzuna quota exhausted - using cache/fallback")
            except Exception as e:
                logger.error(f"❌ Adzuna API failed: {e}")

            # Try The Muse for specific industries
            if search_params.get('industry') in INDUSTRY_API_MAPPING:
                try:
                    muse_category = INDUSTRY_API_MAPPING[search_params['industry']]['muse_category']
                    logger.info(f"🔄 Calling Muse API for category: {muse_category}")
                    muse_jobs = await call_muse_with_tracking(muse(client, muse_category, location))

                    for job in muse_jobs:
                        mapped_job = map_muse_job(job)
                        mapped_job['api_source'] = 'muse'
                        jobs.append(mapped_job)

                    apis_called.append('muse')
                    logger.info(f"✅ Muse: {len(muse_jobs)} jobs")

                except APILimitReached:
                    logger.warning("🚫 Muse quota exhausted - using cache/fallback")
                except Exception as e:
                    logger.error(f"❌ Muse API failed: {e}")

    logger.info(f"📊 Total jobs collected: {len(jobs)} from APIs: {', '.join(apis_called)}")
    return jobs

def filter_and_rank_jobs(jobs: List[Dict], user_preferences: Dict) -> List[Dict]:
    """Filter jobs by user preferences and rank by relevance"""
    filtered_jobs = []
    
    min_salary = user_preferences.get('min_salary', 0)
    max_salary = user_preferences.get('max_salary', 999999)
    preferred_locations = [loc.lower() for loc in user_preferences.get('preferred_locations', [])]
    preferred_job_types = [jt.lower() for jt in user_preferences.get('preferred_job_types', [])]
    
    for job in jobs:
        # Salary filter
        job_salary = job.get('salary', '')
        if job_salary and isinstance(job_salary, str):
            # Extract numbers from salary string
            import re
            salary_numbers = re.findall(r'\d+', job_salary.replace(',', ''))
            if salary_numbers:
                job_salary_num = int(salary_numbers[0])
                if job_salary_num < min_salary or job_salary_num > max_salary:
                    continue
        
        # Location filter
        job_location = job.get('location', '').lower()
        if preferred_locations and not any(loc in job_location for loc in preferred_locations) and 'remote' not in job_location:
            continue
        
        # Job type filter
        job_title = job.get('title', '').lower()
        if preferred_job_types and not any(jt in job_title for jt in preferred_job_types):
            continue
        
        # Calculate relevance score
        score = 0
        
        # Location match
        if any(loc in job_location for loc in preferred_locations):
            score += 30
        if 'remote' in job_location and is_remote_preferred(user_preferences):
            score += 25
        
        # Job type match
        if any(jt in job_title for jt in preferred_job_types):
            score += 20
        
        # Industry match
        job_description = job.get('description', '').lower()
        preferred_industries = [ind.lower() for ind in user_preferences.get('preferred_industries', [])]
        if any(ind in job_description for ind in preferred_industries):
            score += 15
        
        # API source bonus (prioritize certain sources)
        api_source = job.get('api_source', '')
        if api_source == 'jooble':
            score += 10  # Jooble often has good job descriptions
        elif api_source == 'muse':
            score += 8   # The Muse has quality jobs
        
        job['relevance_score'] = score
        filtered_jobs.append(job)
    
    # Sort by relevance score and posting date
    return sorted(filtered_jobs, key=lambda x: (x.get('relevance_score', 0), x.get('posted_date', '')), reverse=True)

def remove_duplicates(jobs: List[Dict]) -> List[Dict]:
    """Remove duplicate jobs based on title and company"""
    seen = set()
    unique_jobs = []
    
    for job in jobs:
        title = job.get('title', '').lower().strip()
        company = job.get('company', '').lower().strip()
        key = f"{title}_{company}"
        
        if key not in seen:
            seen.add(key)
            unique_jobs.append(job)
    
    return unique_jobs

@router.post("/personalized-search", response_model=APIResponse)
async def personalized_job_search(request: PersonalizedSearchRequest):
    """
    Main endpoint for personalized job search with Redis cache and rate limiting
    """
    try:
        # Initialize cache connection if needed
        await init_cache()

        # Get user preferences
        prefs_response = supabase.table("user_job_preferences").select("*").eq("user_id", request.user_id).execute()

        if not prefs_response.data:
            raise HTTPException(status_code=404, detail="User preferences not found")

        user_preferences = prefs_response.data[0]
        logger.info(f"🎯 Personalized search for user: {request.user_id}")

        # Check user rate limits (Phase 3-D)
        user_plan = user_preferences.get('plan', 'free')  # Default to free plan
        daily_search_limit = 5 if user_plan == 'free' else 50 if user_plan == 'plus' else 150

        searches_today = await get_user_search_count(request.user_id)

        if searches_today >= daily_search_limit and not request.force_refresh:
            logger.warning(f"🚫 User {request.user_id} exceeded daily limit ({searches_today}/{daily_search_limit})")

            # Try to return cached results if available
            locations = user_preferences.get('preferred_locations', ['Remote'])[:1]
            job_types = user_preferences.get('preferred_job_types', ['Full-time'])[:1]
            experience = user_preferences.get('experience_level', 'mid')
            industries = user_preferences.get('preferred_industries', [])[:1]

            cached_jobs = await get_cached_jobs(locations[0] if locations else 'Remote',
                                              job_types, experience, industries)

            if cached_jobs:
                return JSONResponse(
                    content={
                        "message": "Daily search limit reached. Upgrade for fresh searches.",
                        "jobs": cached_jobs.get('data', {}).get('jobs', [])[:request.limit],
                        "cache_used": True,
                        "upgrade_required": True,
                        "searches_used": searches_today,
                        "search_limit": daily_search_limit
                    },
                    status_code=429
                )
            else:
                return JSONResponse(
                    content={
                        "message": "Daily search limit reached. Please upgrade your plan.",
                        "jobs": [],
                        "upgrade_required": True,
                        "searches_used": searches_today,
                        "search_limit": daily_search_limit
                    },
                    status_code=429
                )

        # Extract search parameters
        locations = user_preferences.get('preferred_locations', ['Remote'])[:3]
        job_types = user_preferences.get('preferred_job_types', ['Full-time'])[:2]
        experience = user_preferences.get('experience_level', 'mid')
        industries = user_preferences.get('preferred_industries', [])[:2]

        # Check Redis cache first (unless force refresh)
        if not request.force_refresh and locations and job_types:
            cached_jobs = await get_cached_jobs(locations[0], job_types, experience, industries)

            if cached_jobs:
                logger.info(f"🎯 Redis cache HIT for user search")
                return APIResponse(
                    jobs=cached_jobs.get('data', {}).get('jobs', [])[:request.limit],
                    total_found=len(cached_jobs.get('data', {}).get('jobs', [])),
                    cache_used=True,
                    apis_called=[],
                    search_combinations=0,
                    user_preferences=user_preferences
                )
        
        # Increment user search count
        await increment_user_searches(request.user_id)

        # Generate search combinations and fetch jobs
        all_jobs = []
        search_combinations = 0
        apis_called = set()

        logger.info(f"🔍 Starting fresh job search for {len(locations)} locations, {len(job_types)} job types")

        for location in locations:
            for job_type in job_types:
                if industries:
                    for industry in industries:
                        search_params = {
                            'location': location,
                            'keywords': f"{industry} {job_type}",
                            'job_type': job_type,
                            'industry': industry
                        }

                        jobs = await call_apis_for_search(search_params, user_preferences)
                        all_jobs.extend(jobs)
                        search_combinations += 1

                        # Add delay to respect rate limits
                        await asyncio.sleep(0.5)
                else:
                    search_params = {
                        'location': location,
                        'keywords': job_type,
                        'job_type': job_type
                    }

                    jobs = await call_apis_for_search(search_params, user_preferences)
                    all_jobs.extend(jobs)
                    search_combinations += 1

                    # Add delay to respect rate limits
                    await asyncio.sleep(0.5)

        # Process results
        unique_jobs = remove_duplicates(all_jobs)
        filtered_jobs = filter_and_rank_jobs(unique_jobs, user_preferences)
        final_jobs = filtered_jobs[:request.limit]

        # Cache results in Redis
        if locations and job_types and final_jobs:
            cache_payload = {
                "jobs": filtered_jobs[:200],  # Cache top 200 jobs
                "user_preferences": user_preferences,
                "search_combinations": search_combinations,
                "cached_at": datetime.now().isoformat()
            }

            await cache_jobs(locations[0], job_types, experience, industries, cache_payload)
            logger.info(f"💾 Cached {len(filtered_jobs[:200])} jobs for future searches")

        logger.info(f"✅ Found {len(final_jobs)} personalized jobs from {search_combinations} searches")

        return APIResponse(
            jobs=final_jobs,
            total_found=len(filtered_jobs),
            cache_used=False,
            apis_called=list(apis_called),
            search_combinations=search_combinations,
            user_preferences=user_preferences
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Personalized search error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")
