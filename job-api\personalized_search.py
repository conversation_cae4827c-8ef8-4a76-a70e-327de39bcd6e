"""
Personalized Job Search Orchestrator
Replaces static job storage with dynamic API calling based on user preferences
"""

import asyncio
import httpx
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from supabase_client import supabase
from jooble_service import jooble, map_jooble_job
from muse_service import muse, map_muse_job
from job_service import adzuna, arbeitnow
import json
from datetime import datetime, timedelta

router = APIRouter(prefix="/api/jobs", tags=["personalized-search"])

class PersonalizedSearchRequest(BaseModel):
    user_id: str
    force_refresh: bool = False
    limit: int = 100

class APIResponse(BaseModel):
    jobs: List[Dict[str, Any]]
    total_found: int
    cache_used: bool
    apis_called: List[str]
    search_combinations: int
    user_preferences: Dict[str, Any]

# Industry to API category mapping
INDUSTRY_API_MAPPING = {
    'Technology': {
        'muse_category': 'Computer and IT',
        'jooble_keywords': ['software', 'developer', 'engineer', 'tech'],
        'adzuna_keywords': ['software', 'developer', 'IT']
    },
    'Healthcare': {
        'muse_category': 'Healthcare',
        'jooble_keywords': ['nurse', 'doctor', 'medical', 'healthcare'],
        'adzuna_keywords': ['healthcare', 'medical', 'nurse']
    },
    'Finance': {
        'muse_category': 'Finance',
        'jooble_keywords': ['finance', 'accounting', 'analyst', 'banking'],
        'adzuna_keywords': ['finance', 'accounting', 'banking']
    },
    'Education': {
        'muse_category': 'Education',
        'jooble_keywords': ['teacher', 'education', 'instructor', 'academic'],
        'adzuna_keywords': ['education', 'teacher', 'academic']
    },
    'Marketing & Advertising': {
        'muse_category': 'Marketing',
        'jooble_keywords': ['marketing', 'advertising', 'digital marketing', 'social media'],
        'adzuna_keywords': ['marketing', 'advertising', 'digital']
    }
}

def is_us_location(location: str) -> bool:
    """Check if location is in the US"""
    us_indicators = [
        ', CA', ', NY', ', TX', ', FL', ', IL', ', PA', ', OH', ', GA', ', NC', ', MI',
        'California', 'New York', 'Texas', 'Florida', 'Illinois', 'Pennsylvania',
        'United States', 'USA', 'US'
    ]
    return any(indicator in location for indicator in us_indicators)

def is_remote_preferred(preferences: Dict) -> bool:
    """Check if user prefers remote work"""
    remote_pref = preferences.get('remote_work_preference', '')
    return remote_pref in ['required', 'preferred'] or 'Remote' in preferences.get('preferred_locations', [])

async def get_cached_search(cache_key: str) -> Optional[Dict]:
    """Check if we have cached results for this search"""
    try:
        response = supabase.table("cached_job_searches").select("*").eq("cache_key", cache_key).execute()
        
        if response.data:
            cache_entry = response.data[0]
            expires_at = datetime.fromisoformat(cache_entry['expires_at'].replace('Z', '+00:00'))
            
            if datetime.now().replace(tzinfo=expires_at.tzinfo) < expires_at:
                # Update user count
                supabase.table("cached_job_searches").update({
                    "user_count": cache_entry['user_count'] + 1,
                    "last_accessed": datetime.now().isoformat()
                }).eq("id", cache_entry['id']).execute()
                
                return cache_entry
        
        return None
    except Exception as e:
        print(f"Cache check error: {e}")
        return None

async def cache_search_results(cache_key: str, search_params: Dict, results: List[Dict]) -> None:
    """Cache search results for future use"""
    try:
        expires_at = datetime.now() + timedelta(hours=4)
        
        supabase.table("cached_job_searches").upsert({
            "cache_key": cache_key,
            "search_params": search_params,
            "results": results,
            "created_at": datetime.now().isoformat(),
            "expires_at": expires_at.isoformat(),
            "user_count": 1,
            "last_accessed": datetime.now().isoformat()
        }).execute()
        
        print(f"✅ Cached {len(results)} jobs with key: {cache_key}")
    except Exception as e:
        print(f"Cache save error: {e}")

def generate_cache_key(user_preferences: Dict) -> str:
    """Generate a cache key based on user preferences"""
    locations = sorted(user_preferences.get('preferred_locations', []))
    job_types = sorted(user_preferences.get('preferred_job_types', []))
    industries = sorted(user_preferences.get('preferred_industries', []))
    experience = user_preferences.get('experience_level', 'mid')
    
    key_parts = [
        '_'.join(locations[:3]),  # Limit to 3 locations
        '_'.join(job_types[:2]),  # Limit to 2 job types
        '_'.join(industries[:2]), # Limit to 2 industries
        experience
    ]
    
    return '_'.join(key_parts).lower().replace(' ', '_').replace(',', '')

async def call_apis_for_search(search_params: Dict, user_preferences: Dict) -> List[Dict]:
    """Call multiple APIs based on search parameters"""
    jobs = []
    apis_called = []
    
    location = search_params['location']
    keywords = search_params['keywords']
    
    async with httpx.AsyncClient() as client:
        tasks = []
        
        # Always call Jooble (good international coverage)
        tasks.append(('jooble', jooble(client, keywords, location)))
        apis_called.append('jooble')
        
        # Call Arbeitnow (good for remote and international)
        tasks.append(('arbeitnow', arbeitnow(client)))
        apis_called.append('arbeitnow')
        
        # US-specific APIs
        if is_us_location(location):
            tasks.append(('adzuna', adzuna(client)))
            apis_called.append('adzuna')
            
            # The Muse for specific industries
            if search_params.get('industry') in INDUSTRY_API_MAPPING:
                muse_category = INDUSTRY_API_MAPPING[search_params['industry']]['muse_category']
                tasks.append(('muse', muse(client, muse_category, location)))
                apis_called.append('muse')
        
        # Execute all API calls
        results = await asyncio.gather(*[task[1] for task in tasks], return_exceptions=True)
        
        # Process results
        for i, (api_name, result) in enumerate(zip([task[0] for task in tasks], results)):
            if isinstance(result, Exception):
                print(f"❌ {api_name} API failed: {result}")
                continue
            
            if not result:
                print(f"⚠️ {api_name} returned no results")
                continue
            
            # Map jobs based on API
            if api_name == 'jooble':
                for job in result:
                    mapped_job = map_jooble_job(job)
                    mapped_job['api_source'] = 'jooble'
                    jobs.append(mapped_job)
            elif api_name == 'muse':
                for job in result:
                    mapped_job = map_muse_job(job)
                    mapped_job['api_source'] = 'muse'
                    jobs.append(mapped_job)
            else:
                # Adzuna and Arbeitnow
                for job in result:
                    job['api_source'] = api_name
                    jobs.append(job)
            
            print(f"✅ {api_name}: {len(result)} jobs")
    
    return jobs

def filter_and_rank_jobs(jobs: List[Dict], user_preferences: Dict) -> List[Dict]:
    """Filter jobs by user preferences and rank by relevance"""
    filtered_jobs = []
    
    min_salary = user_preferences.get('min_salary', 0)
    max_salary = user_preferences.get('max_salary', 999999)
    preferred_locations = [loc.lower() for loc in user_preferences.get('preferred_locations', [])]
    preferred_job_types = [jt.lower() for jt in user_preferences.get('preferred_job_types', [])]
    
    for job in jobs:
        # Salary filter
        job_salary = job.get('salary', '')
        if job_salary and isinstance(job_salary, str):
            # Extract numbers from salary string
            import re
            salary_numbers = re.findall(r'\d+', job_salary.replace(',', ''))
            if salary_numbers:
                job_salary_num = int(salary_numbers[0])
                if job_salary_num < min_salary or job_salary_num > max_salary:
                    continue
        
        # Location filter
        job_location = job.get('location', '').lower()
        if preferred_locations and not any(loc in job_location for loc in preferred_locations) and 'remote' not in job_location:
            continue
        
        # Job type filter
        job_title = job.get('title', '').lower()
        if preferred_job_types and not any(jt in job_title for jt in preferred_job_types):
            continue
        
        # Calculate relevance score
        score = 0
        
        # Location match
        if any(loc in job_location for loc in preferred_locations):
            score += 30
        if 'remote' in job_location and is_remote_preferred(user_preferences):
            score += 25
        
        # Job type match
        if any(jt in job_title for jt in preferred_job_types):
            score += 20
        
        # Industry match
        job_description = job.get('description', '').lower()
        preferred_industries = [ind.lower() for ind in user_preferences.get('preferred_industries', [])]
        if any(ind in job_description for ind in preferred_industries):
            score += 15
        
        # API source bonus (prioritize certain sources)
        api_source = job.get('api_source', '')
        if api_source == 'jooble':
            score += 10  # Jooble often has good job descriptions
        elif api_source == 'muse':
            score += 8   # The Muse has quality jobs
        
        job['relevance_score'] = score
        filtered_jobs.append(job)
    
    # Sort by relevance score and posting date
    return sorted(filtered_jobs, key=lambda x: (x.get('relevance_score', 0), x.get('posted_date', '')), reverse=True)

def remove_duplicates(jobs: List[Dict]) -> List[Dict]:
    """Remove duplicate jobs based on title and company"""
    seen = set()
    unique_jobs = []
    
    for job in jobs:
        title = job.get('title', '').lower().strip()
        company = job.get('company', '').lower().strip()
        key = f"{title}_{company}"
        
        if key not in seen:
            seen.add(key)
            unique_jobs.append(job)
    
    return unique_jobs

@router.post("/personalized-search", response_model=APIResponse)
async def personalized_job_search(request: PersonalizedSearchRequest):
    """
    Main endpoint for personalized job search
    Replaces static job storage with dynamic API calling
    """
    try:
        # Get user preferences
        prefs_response = supabase.table("user_job_preferences").select("*").eq("user_id", request.user_id).execute()
        
        if not prefs_response.data:
            raise HTTPException(status_code=404, detail="User preferences not found")
        
        user_preferences = prefs_response.data[0]
        print(f"🎯 Personalized search for user: {request.user_id}")
        
        # Generate cache key
        cache_key = generate_cache_key(user_preferences)
        
        # Check cache first (unless force refresh)
        if not request.force_refresh:
            cached_result = await get_cached_search(cache_key)
            if cached_result:
                print(f"✅ Cache hit for key: {cache_key}")
                return APIResponse(
                    jobs=cached_result['results'][:request.limit],
                    total_found=len(cached_result['results']),
                    cache_used=True,
                    apis_called=[],
                    search_combinations=0,
                    user_preferences=user_preferences
                )
        
        # Generate search combinations
        all_jobs = []
        search_combinations = 0
        apis_called = set()
        
        # Create search combinations based on user preferences
        locations = user_preferences.get('preferred_locations', ['Remote'])[:3]  # Limit to 3
        job_types = user_preferences.get('preferred_job_types', ['Full-time'])[:2]  # Limit to 2
        industries = user_preferences.get('preferred_industries', [])[:2]  # Limit to 2
        
        for location in locations:
            for job_type in job_types:
                if industries:
                    for industry in industries:
                        search_params = {
                            'location': location,
                            'keywords': f"{industry} {job_type}",
                            'job_type': job_type,
                            'industry': industry
                        }
                        
                        jobs = await call_apis_for_search(search_params, user_preferences)
                        all_jobs.extend(jobs)
                        search_combinations += 1
                        
                        # Add delay to respect rate limits
                        await asyncio.sleep(0.5)
                else:
                    search_params = {
                        'location': location,
                        'keywords': job_type,
                        'job_type': job_type
                    }
                    
                    jobs = await call_apis_for_search(search_params, user_preferences)
                    all_jobs.extend(jobs)
                    search_combinations += 1
                    
                    # Add delay to respect rate limits
                    await asyncio.sleep(0.5)
        
        # Process results
        unique_jobs = remove_duplicates(all_jobs)
        filtered_jobs = filter_and_rank_jobs(unique_jobs, user_preferences)
        final_jobs = filtered_jobs[:request.limit]
        
        # Cache results
        await cache_search_results(cache_key, {
            'user_preferences': user_preferences,
            'search_combinations': search_combinations
        }, filtered_jobs[:200])  # Cache top 200 jobs
        
        print(f"✅ Found {len(final_jobs)} personalized jobs from {search_combinations} searches")
        
        return APIResponse(
            jobs=final_jobs,
            total_found=len(filtered_jobs),
            cache_used=False,
            apis_called=list(apis_called),
            search_combinations=search_combinations,
            user_preferences=user_preferences
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Personalized search error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")
